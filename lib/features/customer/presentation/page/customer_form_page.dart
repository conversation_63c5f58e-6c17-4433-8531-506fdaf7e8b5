import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobil_kochat/core/theme/app_colors.dart';
import 'package:mobil_kochat/core/utils/app_constants.dart';

import '../../../../di/dependency_injection.dart';
import '../../models/customer_create_request.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';

// Improved Phone number formatter
class PhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;
    final oldText = oldValue.text;
    final selection = newValue.selection;

    // Don't allow deletion of +998 prefix
    if (!text.startsWith('+998')) {
      return TextEditingValue(
        text: '+998 ',
        selection: TextSelection.collapsed(offset: 5),
      );
    }

    // If user tries to place cursor before +998, move it after
    if (selection.baseOffset < 5) {
      return TextEditingValue(
        text: oldText,
        selection: TextSelection.collapsed(offset: 5),
      );
    }

    // Count digits before cursor in the new text
    int digitCountBeforeCursor = 0;
    int cursorPos = selection.baseOffset.clamp(0, text.length);

    for (int i = 5; i < cursorPos; i++) {
      // Start from position 5 (after "+998 ")
      if (i < text.length && RegExp(r'\d').hasMatch(text[i])) {
        digitCountBeforeCursor++;
      }
    }

    // Extract only digits after +998
    final digitsOnly = text.substring(4).replaceAll(RegExp(r'[^\d]'), '');

    // Limit to 9 digits (Uzbek phone number format)
    final limitedDigits =
        digitsOnly.length > 9 ? digitsOnly.substring(0, 9) : digitsOnly;

    String formatted = '+998';

    if (limitedDigits.isNotEmpty) {
      formatted +=
          ' ${limitedDigits.substring(0, limitedDigits.length >= 2 ? 2 : limitedDigits.length)}';

      if (limitedDigits.length > 2) {
        formatted +=
            ' ${limitedDigits.substring(2, limitedDigits.length >= 5 ? 5 : limitedDigits.length)}';
      }

      if (limitedDigits.length > 5) {
        formatted +=
            '-${limitedDigits.substring(5, limitedDigits.length >= 7 ? 7 : limitedDigits.length)}';
      }

      if (limitedDigits.length > 7) {
        formatted += '-${limitedDigits.substring(7)}';
      }
    } else {
      formatted += ' ';
    }

    // Calculate correct cursor position based on digit count
    int newCursorPos = 5; // Start after "+998 "
    int digitsPlaced = 0;

    for (int i = 5; i < formatted.length; i++) {
      if (RegExp(r'\d').hasMatch(formatted[i])) {
        digitsPlaced++;
        if (digitsPlaced >= digitCountBeforeCursor) {
          newCursorPos = i + 1;
          break;
        }
      } else if (digitsPlaced < digitCountBeforeCursor) {
        // Move cursor past non-digit characters only if we haven't reached the target digit count
        newCursorPos = i + 1;
      }
    }

    // If we've placed all target digits, cursor should be at the end
    if (digitsPlaced < digitCountBeforeCursor) {
      newCursorPos = formatted.length;
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: newCursorPos),
    );
  }
}

// JSHSHIR formatter
class JshshirInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;
    final numericOnly = text.replaceAll(RegExp(r'[^\d]'), '');
    final limitedNumeric =
        numericOnly.length > 14 ? numericOnly.substring(0, 14) : numericOnly;

    return TextEditingValue(
      text: limitedNumeric,
      selection: TextSelection.collapsed(offset: limitedNumeric.length),
    );
  }
}

// Passport formatter
class PassportInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Calculate digit/letter count before cursor
    final String newText = newValue.text;
    final int offset = newValue.selection.baseOffset.clamp(0, newText.length);
    int charCountBeforeCursor = 0;
    for (int i = 0; i < offset; i++) {
      final String char = newText[i];
      if (RegExp(r'[A-Z0-9]').hasMatch(char)) {
        charCountBeforeCursor++;
      }
    }

    // Original formatting logic
    String text = newValue.text.toUpperCase();
    text = text.replaceAll(RegExp(r'[^A-Z0-9]'), '');

    String formatted = '';

    if (text.length >= 1) {
      // First two characters should be letters
      final letters = text.substring(0, text.length > 2 ? 2 : text.length);
      formatted += letters.replaceAll(RegExp(r'[^A-Z]'), '');

      if (formatted.length == 2 && text.length > 2) {
        formatted += ' ';
        // Next 7 characters should be numbers
        final numbers = text.substring(2);
        final numericOnly = numbers.replaceAll(RegExp(r'[^\d]'), '');
        final limitedNumbers =
            numericOnly.length > 7 ? numericOnly.substring(0, 7) : numericOnly;
        formatted += limitedNumbers;
      }
    }

    // Calculate new cursor position
    int newOffset = 0;
    int charsPlaced = 0;
    for (int i = 0; i < formatted.length; i++) {
      final String char = formatted[i];
      if (RegExp(r'[A-Z0-9]').hasMatch(char)) {
        charsPlaced++;
      }
      if (charsPlaced >= charCountBeforeCursor) {
        newOffset = i + 1;
        break;
      }
    }
    if (charsPlaced < charCountBeforeCursor) {
      newOffset = formatted.length;
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: newOffset),
    );
  }
}

class CustomerFormPage extends StatefulWidget {
  final Customer? customer;

  const CustomerFormPage({super.key, this.customer});

  @override
  State<CustomerFormPage> createState() => _CustomerFormPageState();
}

class _CustomerFormPageState extends State<CustomerFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  final _customerService = CustomerService(
      dio: di(), // Your Dio instance,
      storage: di(), // Your GetStorage instance,
      networkInfo: di() // Your NetworkInfo instance,
      );

// Form controllers
  final _fullNameController = TextEditingController();
  final _birthDayController = TextEditingController();
  final _passportController = TextEditingController();
  final _phoneController = TextEditingController();
  final _jshshirController = TextEditingController();
  final _addressController = TextEditingController();

// Dropdown values
  Province? _selectedProvince;
  Region? _selectedRegion;

// Data lists
  List<Province> _provinces = [];
  List<Region> _regions = [];

// Loading states
  bool _isLoadingProvinces = true;
  bool _isLoadingRegions = false;
  bool _isCreating = false;

  bool get _isEdit => widget.customer != null;

  @override
  void initState() {
    super.initState();
    _initData();
  }

  Future<void> _initData() async {
    print('Starting initialization...');

    // First, load all provinces
    await _loadProvinces();
    print('Provinces loaded: ${_provinces.length}');

    if (_isEdit) {
      print('Edit mode - populating data...');
      await _populateEditData();
      print('Edit data populated');
    } else {
      print('Create mode - setting default phone');
      _phoneController.text = '+998 ';
    }

    print('Initialization complete');
  }

  Future<void> _populateEditData() async {
    final cust = widget.customer!;

    // Set basic fields
    _fullNameController.text = cust.fullName;
    _birthDayController.text = cust.birthDay ?? '';
    _passportController.text = _formatPassport(cust.passport);
    _phoneController.text = _formatPhone(cust.phone);
    _jshshirController.text = cust.jshshir ?? '';
    _addressController.text = cust.address;

    // Debug prints to check customer data
    print('Customer province: ${cust.province?.id}');
    print('Customer region: ${cust.region?.id}');
    print(
        'Available provinces: ${_provinces.map((p) => '${p.id}: ${p.title}').toList()}');

    // Set province and region if they exist
    if (cust.province != null && cust.province!.id.isNotEmpty) {
      // Find province by ID - Fixed comparison
      Province? foundProvince;
      for (var province in _provinces) {
        if (province.id == cust.province!.id) {
          // Compare with province object's id
          foundProvince = province;
          break;
        }
      }

      if (foundProvince != null) {
        _selectedProvince = foundProvince;
        print('Province found and set: ${foundProvince.title}');

        // Load regions for this province
        await _loadRegionsForProvince(foundProvince.id);

        print(
            'Available regions: ${_regions.map((r) => '${r.id}: ${r.title}').toList()}');

        // Set region if it exists - Fixed comparison
        if (cust.region != null && cust.region!.id.isNotEmpty) {
          Region? foundRegion;
          for (var region in _regions) {
            if (region.id == cust.region!.id) {
              // Compare with region object's id
              foundRegion = region;
              break;
            }
          }

          if (foundRegion != null) {
            _selectedRegion = foundRegion;
            print('Region found and set: ${foundRegion.title}');
          } else {
            print('Region not found: ${cust.region!.id}');
            _selectedRegion = null;
          }
        }
      } else {
        print('Province not found: ${cust.province!.id}');
        _selectedProvince = null;
      }
    }

    // Force UI update
    if (mounted) {
      setState(() {});
    }
  }

  String _formatPhone(String rawPhone) {
    if (rawPhone.isEmpty) return '+998 ';
    if (rawPhone.length != 9) return '+998 ';
    return '+998 ${rawPhone.substring(0, 2)} ${rawPhone.substring(2, 5)}-${rawPhone.substring(5, 7)}-${rawPhone.substring(7)}';
  }

  String _formatPassport(String rawPassport) {
    if (rawPassport.length != 9) return '';
    return '${rawPassport.substring(0, 2)} ${rawPassport.substring(2)}';
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _birthDayController.dispose();
    _passportController.dispose();
    _phoneController.dispose();
    _jshshirController.dispose();
    _addressController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadProvinces() async {
    setState(() {
      _isLoadingProvinces = true;
    });

    try {
      final provinces = await _customerService.getProvinces();
      print('Loaded ${provinces.length} provinces from service');

      setState(() {
        _provinces = provinces;
        _isLoadingProvinces = false;
      });

      print('Provinces set in state: ${_provinces.length}');
    } catch (e) {
      print('Error loading provinces: $e');
      setState(() {
        _isLoadingProvinces = false;
      });
      if (mounted) {
        _showErrorSnackBar('Viloyatlarni yuklashda xato: $e');
      }
    }
  }

  Future<void> _loadRegionsForProvince(String provinceId) async {
    print('Loading regions for province: $provinceId');

    setState(() {
      _isLoadingRegions = true;
    });

    try {
      final regions = await _customerService.getRegions(
        provinceId: provinceId,
      );

      print('Loaded ${regions.length} regions for province $provinceId');

      setState(() {
        _regions = regions;
        _isLoadingRegions = false;
      });

      print('Regions set in state: ${_regions.length}');
    } catch (e) {
      print('Error loading regions: $e');
      setState(() {
        _isLoadingRegions = false;
      });
      if (mounted) {
        _showErrorSnackBar('Tuman/Shahar yuklashda xato: $e');
      }
    }
  }

  Future<void> _onProvinceChanged(Province? province) async {
    setState(() {
      _selectedProvince = province;
      _selectedRegion = null;
      _regions = [];
    });

    if (province != null) {
      await _loadRegionsForProvince(province.id);
    }
  }

  void _onRegionChanged(Region? region) {
    setState(() {
      _selectedRegion = region;
    });
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.cFirstColor,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      _birthDayController.text =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  String _getCleanPhone(String formattedPhone) {
    // Extract only numbers from formatted phone
    final numbers = formattedPhone.replaceAll(RegExp(r'[^\d]'), '');
    // Remove country code 998 and return 9-digit number
    if (numbers.startsWith('998') && numbers.length >= 12) {
      return numbers.substring(3);
    }
    return numbers.length >= 9
        ? numbers.substring(numbers.length - 9)
        : numbers;
  }

  String _getCleanPassport(String formattedPassport) {
    // Remove spaces from passport
    return formattedPassport.replaceAll(' ', '');
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedProvince == null || _selectedRegion == null) {
        _showErrorSnackBar('Viloyat va Tuman/Shahar tanlang');
        return;
      }

      setState(() {
        _isCreating = true;
      });

      try {
        final request = CustomerCreateRequest(
          fullName: _fullNameController.text.trim(),
          birthDay: _birthDayController.text.trim(),
          passport: _getCleanPassport(_passportController.text.trim()),
          phone: _getCleanPhone(_phoneController.text.trim()),
          jshshir: _jshshirController.text.trim(),
          address: _addressController.text.trim(),
          province: _selectedProvince!.id,
          region: _selectedRegion!.id,
        );

        bool success;
        if (_isEdit) {
          success = await _customerService.updateCustomer(
              widget.customer!.id, request);
        } else {
          success = await _customerService.createCustomer(request);
        }

        if (success) {
          _showSuccessSnackBar(_isEdit
              ? 'Mijoz muvaffaqiyatli yangilandi!'
              : 'Mijoz muvaffaqiyatli qo\'shildi!');
          Navigator.pop(context, true);
        } else {
          _showErrorSnackBar('Mijoz saqlashda xato yuz berdi');
        }
      } catch (e) {
        _showErrorSnackBar('Xato: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isCreating = false;
          });
        }
      }
    }
  }

  Widget _buildFormCard(String title, IconData icon, List<Widget> children) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.cFirstColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required IconData prefixIcon,
    String? hintText,
    TextInputType keyboardType = TextInputType.text,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    bool readOnly = false,
    VoidCallback? onTap,
    Widget? suffixIcon,
    int maxLines = 1,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          prefixIcon: Icon(prefixIcon, color: AppColors.cFirstColor),
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.cFirstColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          labelStyle: TextStyle(color: Colors.grey.shade700),
        ),
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        validator: validator,
        readOnly: readOnly,
        onTap: onTap,
        maxLines: maxLines,
        style: const TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildDropdownField<T>({
    required String labelText,
    required IconData prefixIcon,
    required T? value,
    required List<T> items,
    required String Function(T) getTitle,
    required void Function(T?) onChanged,
    String? Function(T?)? validator,
    bool isLoading = false,
    bool enabled = true,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: DropdownButtonFormField<T>(
        value: value,
        decoration: InputDecoration(
          labelText: labelText,
          prefixIcon: Icon(prefixIcon, color: AppColors.cFirstColor),
          suffixIcon: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: Padding(
                    padding: EdgeInsets.all(12.0),
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.cFirstColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          labelStyle: TextStyle(color: Colors.grey.shade700),
        ),
        items: items.map((item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(
              getTitle(item),
              style: const TextStyle(fontSize: 16),
            ),
          );
        }).toList(),
        onChanged: enabled && !isLoading ? onChanged : null,
        validator: validator,
        dropdownColor: Colors.white,
        style: const TextStyle(color: Colors.black87, fontSize: 16),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Ma\'lumotlar yuklanmoqda...',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header Section - Similar to CustomerPage
          Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.fromLTRB(16, 50, 16, 20),
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.cFirstColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(cRadius36),
                bottomRight: Radius.circular(cRadius36),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _isEdit ? 'Mijozni tahrirlash' : 'Yangi mijoz qo\'shish',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Form Content
          Expanded(
            child: _isLoadingProvinces
                ? _buildLoadingState()
                : SingleChildScrollView(
                    controller: _scrollController,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          const SizedBox(height: 16),

                          // Personal Information Card
                          _buildFormCard(
                            'Shaxsiy ma\'lumotlar',
                            Icons.person_outline,
                            [
                              _buildTextField(
                                controller: _fullNameController,
                                labelText: 'To\'liq ismi *',
                                prefixIcon: Icons.person,
                                hintText: 'Familiya Ism Sharif',
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'To\'liq ismni kiriting';
                                  }
                                  if (value.trim().length < 3) {
                                    return 'Ism juda qisqa';
                                  }
                                  return null;
                                },
                              ),
                              _buildTextField(
                                controller: _birthDayController,
                                labelText: 'Tug\'ilgan sana *',
                                prefixIcon: Icons.calendar_today,
                                hintText: 'YYYY-MM-DD',
                                readOnly: true,
                                onTap: _selectDate,
                                suffixIcon: const Icon(Icons.arrow_drop_down),
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Tug\'ilgan sanani tanlang';
                                  }
                                  return null;
                                },
                              ),
                              _buildTextField(
                                controller: _passportController,
                                labelText: 'Passport seriyasi *',
                                prefixIcon: Icons.credit_card,
                                hintText: 'AB 1234567',
                                inputFormatters: [PassportInputFormatter()],
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Passport seriyasini kiriting';
                                  }
                                  final clean = value.replaceAll(' ', '');
                                  if (clean.length != 9) {
                                    return 'Passport formati: AB 1234567';
                                  }
                                  if (!RegExp(r'^[A-Z]{2}\d{7}$')
                                      .hasMatch(clean)) {
                                    return 'Passport formati noto\'g\'ri';
                                  }
                                  return null;
                                },
                              ),
                              _buildTextField(
                                controller: _jshshirController,
                                labelText: 'JSHSHIR *',
                                prefixIcon: Icons.fingerprint,
                                hintText: '12345678901234',
                                keyboardType: TextInputType.number,
                                inputFormatters: [JshshirInputFormatter()],
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'JSHSHIR ni kiriting';
                                  }
                                  if (value.length != 14) {
                                    return 'JSHSHIR 14 ta raqamdan iborat bo\'lishi kerak';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),

                          // Contact Information Card
                          _buildFormCard(
                            'Aloqa ma\'lumotlari',
                            Icons.contact_phone_outlined,
                            [
                              _buildTextField(
                                controller: _phoneController,
                                labelText: 'Telefon raqami *',
                                prefixIcon: Icons.phone,
                                hintText: '+998 91 657-43-73',
                                keyboardType: TextInputType.phone,
                                inputFormatters: [PhoneInputFormatter()],
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Telefon raqamini kiriting';
                                  }
                                  final numbers =
                                      value.replaceAll(RegExp(r'[^\d]'), '');
                                  if (numbers.length < 12) {
                                    return 'To\'liq telefon raqamini kiriting';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),

                          // Location Information Card
                          _buildFormCard(
                            'Manzil ma\'lumotlari',
                            Icons.location_on_outlined,
                            [
                              _buildDropdownField<Province>(
                                labelText: 'Viloyat *',
                                prefixIcon: Icons.location_city,
                                value: _selectedProvince,
                                items: _provinces,
                                getTitle: (province) => province.title,
                                onChanged: _onProvinceChanged,
                                validator: (value) {
                                  if (value == null) {
                                    return 'Viloyatni tanlang';
                                  }
                                  return null;
                                },
                              ),
                              _buildDropdownField<Region>(
                                labelText: 'Tuman/Shahar *',
                                prefixIcon: Icons.location_on,
                                value: _selectedRegion,
                                items: _regions,
                                getTitle: (region) => region.title,
                                onChanged: _onRegionChanged,
                                isLoading: _isLoadingRegions,
                                enabled: _selectedProvince != null,
                                validator: (value) {
                                  if (value == null) {
                                    return 'Tuman/Shahar tanlang';
                                  }
                                  return null;
                                },
                              ),
                              _buildTextField(
                                controller: _addressController,
                                labelText: 'To\'liq manzil *',
                                prefixIcon: Icons.home,
                                hintText: 'Ko\'cha, uy raqami, xonadon...',
                                maxLines: 3,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'To\'liq manzilni kiriting';
                                  }
                                  if (value.trim().length < 10) {
                                    return 'Manzil juda qisqa';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),

                          const SizedBox(height: 24),

                          // Submit Button
                          Container(
                            margin: const EdgeInsets.all(16),
                            height: 56,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: LinearGradient(
                                colors: _isCreating
                                    ? [
                                        Colors.grey.shade400,
                                        Colors.grey.shade500
                                      ]
                                    : [
                                        AppColors.cFirstColor,
                                        AppColors.cFirstColor.withOpacity(0.8)
                                      ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              boxShadow: _isCreating
                                  ? null
                                  : [
                                      BoxShadow(
                                        color: AppColors.cFirstColor
                                            .withOpacity(0.3),
                                        spreadRadius: 0,
                                        blurRadius: 12,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: _isCreating ? null : _submitForm,
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  alignment: Alignment.center,
                                  child: _isCreating
                                      ? const Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              height: 20,
                                              width: 20,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                color: Colors.white,
                                              ),
                                            ),
                                            SizedBox(width: 12),
                                            Text(
                                              'Saqlanmoqda...',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        )
                                      : Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            const Icon(
                                              Icons.save,
                                              color: Colors.white,
                                              size: 20,
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              _isEdit
                                                  ? 'Yangilash'
                                                  : 'Mijozni saqlash',
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
